#!/usr/bin/env python3
"""
测试地形修改的脚本
验证圆角楼梯现在是根据列数而不是行数来决定的
"""

import numpy as np
import sys
import os

# 添加项目路径
sys.path.append('/root/yujun.zhang/code/VitaHIMloco')

from legged_gym.utils.terrain import Terrain
from legged_gym.envs.base.legged_robot_config import LeggedRobotCfg

def test_terrain_modification():
    """测试地形修改"""
    
    # 创建一个简单的地形配置
    cfg = LeggedRobotCfg.terrain()
    cfg.num_rows = 4
    cfg.num_cols = 4
    cfg.terrain_proportions = [0.1, 0.1, 0.2, 0.2, 1.0, 0.0, 0.0, 0.0]  # 主要是阶梯地形
    cfg.curriculum = False
    cfg.selected = False
    
    # 创建地形
    terrain = Terrain(cfg, num_robots=16)
    
    print("地形速度偏置矩阵 (terrain_velocity_bias):")
    print("行索引 (rows) ->")
    print("列索引 (cols) ↓")
    print(terrain.terrain_velocity_bias)
    print()
    
    # 分析结果
    print("分析结果:")
    print("根据修改后的逻辑，圆角楼梯应该根据列数决定:")
    print("- 偶数列 (0, 2, ...): 使用圆角楼梯，速度偏置 = 1.0 (前进)")
    print("- 奇数列 (1, 3, ...): 使用普通楼梯，速度偏置 = -1.0 (后退)")
    print()
    
    # 检查每个位置
    for row in range(cfg.num_rows):
        for col in range(cfg.num_cols):
            bias = terrain.terrain_velocity_bias[row, col]
            if bias != 0:  # 只显示阶梯地形
                stair_type = "圆角楼梯" if bias > 0 else "普通楼梯"
                direction = "前进" if bias > 0 else "后退"
                expected_col_type = "偶数列" if col % 2 == 0 else "奇数列"
                print(f"位置 ({row}, {col}): {stair_type}, 速度偏置={bias:.1f} ({direction}), {expected_col_type}")
    
    print()
    
    # 验证逻辑是否正确
    correct = True
    for row in range(cfg.num_rows):
        for col in range(cfg.num_cols):
            bias = terrain.terrain_velocity_bias[row, col]
            if bias != 0:  # 只检查阶梯地形
                expected_bias = 1.0 if col % 2 == 0 else -1.0
                if bias != expected_bias:
                    print(f"错误: 位置 ({row}, {col}) 的速度偏置应该是 {expected_bias}，但实际是 {bias}")
                    correct = False
    
    if correct:
        print("✅ 验证通过！圆角楼梯现在正确地根据列数来决定。")
    else:
        print("❌ 验证失败！存在不符合预期的地形配置。")
    
    return correct

if __name__ == "__main__":
    test_terrain_modification()
